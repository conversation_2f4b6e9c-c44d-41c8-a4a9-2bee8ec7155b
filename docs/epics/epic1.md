## Epik 1: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi ve Kimlik Doğrulama

**Epik Hedefi**: Projenin temel altyapısını kurmak, kullan<PERSON><PERSON><PERSON>lar<PERSON>n sisteme güvenli bir şekilde kaydolmasını, g<PERSON><PERSON>ını ve oturumlarını yönetmesini sağlamak. Bu epik tamamlandığında, temel kullanıcı işlemleri için test edilebilir ve güvenli bir API'miz olacak.

### Hikaye 1.1: Yeni Kullanıcı Kaydı

**Bir ziyaretçi olarak,** sisteme yeni bir kullanıcı hesabı oluşturabilmek için kaydolmak istiyorum, **böylece** mesajlaşma sistemini kullanmaya başlayabilirim. [cite: 1312, 1361]

#### Kabul Kriterleri

- 1.  `POST /api/auth/register` endpoint'i üzerinden `kull<PERSON><PERSON><PERSON><PERSON> adı`, `e-posta` ve `ş<PERSON><PERSON>` bil<PERSON>leriyle yeni bir kullanıcı oluşturulmalıdır. [cite: 1312]
- 2. Kullanıcı şifresi veritabanına kaydedilmeden önce güvenli bir şekilde hash'lenmelidir.
- 3.  Kayıt işlemi başarılı olduğunda, kullanıcıya bir `Erişim Tokenı` ve `Yenileme Tokenı` döndürülmelidir. [cite: 1362]
- 4. Aynı e-posta veya kullanıcı adıyla tekrar kayıt oluşturulmaya çalışıldığında sistem bir hata mesajı döndürmelidir.

### Hikaye 1.2: Kullanıcı Girişi ve Token Alma

**Kayıtlı bir kullanıcı olarak,** `e-posta` ve `şifremi` kullanarak sisteme giriş yapmak istiyorum, **böylece** kimliğimi doğrulayarak güvenli bir oturum başlatabilirim. [cite: 1313, 1362]

#### Kabul Kriterleri

- 1.  `POST /api/auth/login` endpoint'i üzerinden kullanıcı girişi yapılabilmelidir. [cite: 1313]
- 2. Sağlanan şifre, veritabanındaki hash'lenmiş şifre ile karşılaştırılarak doğrulanmalıdır.
- 3.  Başarılı girişte kullanıcıya yeni bir `Erişim Tokenı` ve `Yenileme Tokenı` verilmelidir. [cite: 1362]
- 4. Yanlış e-posta veya şifre girildiğinde uygun bir hata mesajı döndürülmelidir.

### Hikaye 1.3: Güvenli API Erişimi için Middleware

**Bir geliştirici olarak,** API endpoint'lerini yetkisiz erişime karşı koruyacak bir kimlik doğrulama (authentication) middleware'i oluşturmak istiyorum, **böylece** sadece geçerli token'a sahip kullanıcıların korumalı kaynaklara erişebilmesini sağlayabilirim. [cite: 1330]

#### Kabul Kriterleri

- 1.  Middleware, gelen isteklerin `Authorization` başlığındaki JWT `Erişim Tokenı`'nı kontrol etmelidir. [cite: 1331, 1332]
- 2.  Token geçerliyse, token içindeki kullanıcı bilgileri (payload) `request` nesnesine eklenmelidir. [cite: 1333]
- 3.  Geçersiz veya süresi dolmuş token durumlarında, sistem `401 Unauthorized` gibi uygun bir HTTP durum kodu döndürmelidir. [cite: 1333]
- 4. Bu middleware, kimlik doğrulaması gerektiren tüm API endpoint'lerine uygulanmalıdır.

### Hikaye 1.4: Kullanıcı Profil Bilgilerini Görüntüleme

**Giriş yapmış bir kullanıcı olarak,** kendi profil bilgilerimi görüntüleyebilmek istiyorum, **böylece** hesap detaylarımı kontrol edebilirim. [cite: 1314, 1365]

#### Kabul Kriterleri

- 1.  `GET /api/auth/me` endpoint'i üzerinden istekte bulunan kullanıcının bilgileri döndürülmelidir. [cite: 1314]
- 2. Bu endpoint, Hikaye 1.3'te oluşturulan kimlik doğrulama middleware'i ile korunmalıdır.
- 3. Yanıt, kullanıcının şifresi gibi hassas bilgileri içermemelidir.

### Hikaye 1.5: Kullanıcı Profil Bilgilerini Güncelleme\*\*

    * **Bir kullanıcı olarak,** profil bilgilerimi (örn. kullanıcı adı) güncellemek istiyorum, **böylece** hesap bilgilerimi güncel tutabilirim.
    * **Kabul Kriterleri:** `PUT` veya `PATCH /api/auth/me` endpoint'i ile kimliği doğrulanmış kullanıcının kendi bilgilerini güncellemesi sağlanmalıdır.

### Hikaye 1.6: Erişim Token'ını Yenileme\*\*

    * **Giriş yapmış bir kullanıcı olarak,** `Yenileme Tokenı`'mı kullanarak süresi dolmuş `Erişim Tokenı`'mı yenilemek istiyorum, **böylece** oturumum kesintiye uğramadan uygulamayı kullanmaya devam edebilirim.
    * **Kabul Kriterleri:** `POST /api/auth/refresh` endpoint'i, geçerli bir `Yenileme Tokenı` karşılığında yeni bir `Erişim Tokenı` üretmelidir.

### Hikaye 1.7: Güvenli Oturum Sonlandırma (Çıkış)\*\*

    *  **Giriş yapmış bir kullanıcı olarak,** sistemden çıkış yapmak istiyorum, **böylece** oturumumu güvenli bir şekilde sonlandırabilirim. [cite: 176]
    * **Kabul Kriterleri:** `POST /api/auth/logout` endpoint'i, kullanıcının `Yenileme Tokenı`'nı geçersiz kılarak oturumu sonlandırmalıdır.

### Hikaye 1.8: Diğer Kullanıcıları Listeleme

**Giriş yapmış bir kullanıcı olarak,** sistemdeki diğer kullanıcıları listeleyebilmek istiyorum, **böylece** kimlerle mesajlaşabileceğimi görebilirim. [cite: 1314]

#### Kabul Kriterleri

- 1.  `GET /api/user/list` endpoint'i üzerinden sistemdeki tüm kullanıcılar listelenmelidir. [cite: 1314]
- 2. Bu endpoint, kimlik doğrulama middleware'i ile korunmalıdır.
- 3. Dönen liste, kullanıcıların şifreleri gibi hassas bilgileri içermemelidir.
- 4. Performans için listeleme işleminde sayfalama (pagination) desteği olmalıdır.

### Hikaye 1.9: Hata Yakalama ve Loglama Altyapısının Kurulumu

**Bir geliştirici olarak,** uygulama genelindeki hataları otomatik olarak yakalayıp merkezi bir sisteme (Sentry gibi) bildiren ve olayları standart bir formatta loglayan bir altyapı kurmak istiyorum, **böylece** sorunları proaktif olarak tespit edip daha hızlı müdahale edebilirim.

#### Kabul Kriterleri

- 1. Projeye bir loglama kütüphanesi (örn. Winston) entegre edilmeli ve temel konfigürasyonu yapılmalıdır.
- 2. Projeye bir hata yakalama hizmeti (örn. Sentry) entegre edilmeli ve Express.js için hata yakalama middleware'i olarak yapılandırılmalıdır.
- 3. Tüm kritik hatalar (uncaught exceptions, unhandled rejections) otomatik olarak yakalanıp Sentry'e bildirilmelidir.
- 4. Geliştirilen diğer endpoint'lerde (örn. kullanıcı kaydı) oluşabilecek manuel hataların nasıl loglanacağına dair bir standart oluşturulmalıdır.
